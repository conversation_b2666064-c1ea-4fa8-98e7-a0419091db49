// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Patient {
  id              String          @id @default(uuid())
  firstName       String
  lastName        String
  birthDate       DateTime
  fiscalCode      String          @unique
  anamnesisSigned Boolean
  attachments     File[]
  appointments    Appointment[]
  invoices        Invoice[]
  udis            Patient_UDI[]
}

model UDI {
  id      String        @id @default(uuid())
  code    String        @unique
  patients Patient_UDI[]
}

model Patient_UDI {
  patientId String
  udiId     String
  @@id([patientId, udiId])
  @@map("patient_udi")
}

model Appointment {
  id        String   @id @default(uuid())
  patientId String
  date      DateTime
  reason    String
  status    String
  patient   Patient  @relation(fields: [patientId], references: [id])
}

model Invoice {
  id        String   @id @default(uuid())
  patientId String
  date      DateTime
  total     Decimal
  patient   Patient  @relation(fields: [patientId], references: [id])
}

model File {
  id         String   @id @default(uuid())
  patientId  String
  type       String
  s3Key      String
  uploadedAt DateTime @default(now())
  patient    Patient  @relation(fields: [patientId], references: [id])
}

model Notification {
  id          String   @id @default(uuid())
  title       String
  channel     String
  scheduledAt DateTime
  sentAt      DateTime?
}
