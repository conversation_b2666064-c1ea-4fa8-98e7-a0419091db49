# MOKO SOSTANZA Dental CRM - Environment Variables Template
# Copia questo file in .env e configura le tue variabili

# ================================
# DATABASE CONFIGURATION
# ================================

# DATABASE_URL per PostgreSQL
# Formato: postgresql://username:password@host:port/database?schema=public

# Per sviluppo locale con PostgreSQL:
DATABASE_URL="postgresql://postgres:password@localhost:5432/moko_dental_dev?schema=public"

# Per Vercel Postgres (configurato automaticamente in produzione):
# DATABASE_URL="postgres://username:password@host:port/database"

# Per Amazon RDS PostgreSQL (configurazione futura):
# DATABASE_URL="************************************************/moko_dental_prod?schema=public"

# ================================
# APPLICATION CONFIGURATION
# ================================

# Ambiente di esecuzione
NODE_ENV="development"

# ================================
# PRISMA CONFIGURATION
# ================================

# URL per Prisma Accelerate (opzionale, per performance avanzate)
# PRISMA_ACCELERATE_URL="prisma://accelerate.prisma-data.net/?api_key=your-api-key"

# ================================
# VERCEL CONFIGURATION
# ================================

# Queste variabili sono configurate automaticamente da Vercel in produzione
# VERCEL="1"
# VERCEL_ENV="production"
# VERCEL_URL="your-app.vercel.app"

# ================================
# ISTRUZIONI
# ================================

# 1. Copia questo file in .env:
#    cp .env.example .env

# 2. Configura DATABASE_URL con le tue credenziali PostgreSQL

# 3. Per sviluppo locale, installa PostgreSQL e crea il database:
#    createdb moko_dental_dev

# 4. Esegui le migrazioni:
#    npm run db:migrate:dev

# 5. Popola il database con dati di esempio:
#    npm run db:seed

# ================================
# SICUREZZA
# ================================

# ⚠️  IMPORTANTE: Non committare mai il file .env nel repository!
# Il file .env è già incluso nel .gitignore per sicurezza.
# Le credenziali del database devono rimanere private.
